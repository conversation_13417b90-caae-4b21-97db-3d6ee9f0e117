"use client";

import { markNotificationAsRead } from "@/actions/notifications";
import { useRouter } from "next/navigation";

export default function NotificationItem({
  notification,
}: {
  notification: any;
}) {
  const router = useRouter();

  const handleClick = async () => {
    if (!notification.isRead) {
      await markNotificationAsRead(notification.id);
    }
    router.push(`/manuscripts/${notification.relatedId}`);
  };

  return (
    <div
      onClick={handleClick}
      className={`p-4 border rounded-lg cursor-pointer ${
        !notification.isRead ? "bg-blue-50 border-blue-200" : "bg-white"
      }`}
    >
      <h3 className="font-medium">{notification.title}</h3>
      <p className="text-sm text-gray-600">{notification.message}</p>
    </div>
  );
}
