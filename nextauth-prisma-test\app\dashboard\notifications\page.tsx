import { auth } from "@/auth";
import NotificationItem from "../../components/NotificationItem";

export default async function NotificationsPage() {
  const session = await auth();
  if (!session?.user?.id) return <div>Unauthorized</div>;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/api/notifications`,
    {
      next: { tags: ["notifications"] },
    }
  );
  const { notifications } = await response.json();

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">Notifications</h1>
      <div className="space-y-2">
        {notifications.map((notification: any) => (
          <NotificationItem key={notification.id} notification={notification} />
        ))}
      </div>
    </div>
  );
}
