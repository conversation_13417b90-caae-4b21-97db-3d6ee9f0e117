import { PDFDocument, StandardFonts, rgb } from "pdf-lib";

/**
 * Generates a PDF from the given content and returns it as a Buffer (Uint8Array).
 */
export async function generateAndStorePdf(
  content: string
): Promise<Uint8Array | undefined> {
  try {
    if (typeof content !== "string" || !content.trim()) {
      console.error("generateAndStorePdf: Invalid or empty content");
      return undefined;
    }
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595, 842]); // A4
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    page.drawText(content, {
      x: 50,
      y: 780,
      size: 12,
      font,
      color: rgb(0, 0, 0),
      maxWidth: 500,
      lineHeight: 14,
    });

    const pdfBytes = await pdfDoc.save(); // Uint8Array
    return pdfBytes;
  } catch (err) {
    console.error("generateAndStorePdf error:", err);
    return undefined;
  }
}
