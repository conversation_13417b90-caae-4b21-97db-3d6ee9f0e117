const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const samplePublications = [
  {
    title: "Advanced Machine Learning Techniques",
    abstract: "A comprehensive guide to cutting-edge machine learning algorithms and applications.",
    content: "This publication explores advanced machine learning techniques...",
    keywords: "machine learning, AI, deep learning, neural networks",
    type: "BOOK",
    cover: "https://example.com/covers/ml-book.jpg" // Optional: add cover URLs
  },
  {
    title: "Sustainable Energy Solutions",
    abstract: "Research on renewable energy technologies and their implementation.",
    content: "This journal examines the latest developments in sustainable energy...",
    keywords: "renewable energy, sustainability, solar power, wind energy",
    type: "JOURNAL"
  },
  {
    title: "Modern Web Development Frameworks",
    abstract: "Analysis of popular web development frameworks and their use cases.",
    content: "This article compares various modern web frameworks...",
    keywords: "web development, React, Vue, Angular, frameworks",
    type: "ARTICLE"
  }
  // Add more publications as needed
];

async function seedPublications() {
  try {
    console.log('🌱 Starting publications seeding...');

    // Get the first user to assign as author (or create a default user)
    let user = await prisma.user.findFirst();
    
    if (!user) {
      console.log('📝 Creating default user...');
      user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Tina Education Admin',
          role: 'ADMIN'
        }
      });
    }

    console.log(`👤 Using user: ${user.name} (${user.email})`);

    // Create publications
    for (const publication of samplePublications) {
      console.log(`📚 Creating publication: ${publication.title}`);
      
      await prisma.publication.create({
        data: {
          ...publication,
          author_id: user.id
        }
      });
    }

    console.log('✅ Publications seeding completed successfully!');
    console.log(`📊 Created ${samplePublications.length} sample publications`);

  } catch (error) {
    console.error('❌ Error seeding publications:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder
seedPublications();