import { auth } from "@/auth";
import Link from "next/link";
import { FaBell } from "react-icons/fa";

export async function NotificationBell() {
  const session = await auth();
  if (!session?.user?.id) return null;

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_BASE_URL}/api/notifications`,
    {
      next: { tags: ["notifications"] },
    }
  );
  const { unreadCount } = await response.json();

  return (
    <Link href="/dashboard/notifications" className="relative">
      <FaBell size={24} color="#000" />
      {unreadCount > 0 && (
        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
          {unreadCount}
        </span>
      )}
    </Link>
  );
}
