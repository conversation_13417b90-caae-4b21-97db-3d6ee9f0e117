// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  // output   = "./generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String          @id @default(cuid())
  name          String?
  email         String          @unique
  emailVerified DateTime?
  image         String?
  role          Role            @default(REVIEWER)
  accounts      Account[]
  sessions      Session[]
  // Optional for WebAuthn support
  Authenticator Authenticator[]
  notifications Notification[]
  manuscripts   Manuscript[]
  reviews       Review[]
  publications  Publication[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

// Optional for WebAuthn support
model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  title     String
  message   String
  isRead    Boolean  @default(false)
  type      String // e.g., "manuscript", "review", "system"
  relatedId String? // ID of related manuscript or other entity
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Manuscript {
  id        String           @id @default(cuid())
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  title     String
  abstract  String
  content   String
  pdfFile   Bytes
  status    ManuscriptStatus @default(DRAFT)
  keywords  String
  author_id String
  user      User             @relation(fields: [author_id], references: [id])
  reviews   Review[]
}

model Review {
  id            String       @id @default(cuid())
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  content       String
  manuscript_id String
  reviewer_id   String
  feedback      String?
  score         Int?
  status        ReviewStatus @default(PENDING)
  manuscript    Manuscript   @relation(fields: [manuscript_id], references: [id])
  user          User         @relation(fields: [reviewer_id], references: [id])

  ReviewerOnReviews ReviewerOnReviews[]
}

model Reviewer {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  name      String
  email     String   @unique
 
  ReviewerOnReviews ReviewerOnReviews[]
}

model ReviewerOnReviews {
  reviewer   Reviewer @relation(fields: [reviewerId], references: [id])
  reviewerId String
  review     Review   @relation(fields: [reviewId], references: [id])
  reviewId   String

  @@id([reviewerId, reviewId])
}

model Publication {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  title     String
  abstract  String?
  content   String?
  keywords  String?
  cover     String?
  author_id String
  type      Pub_type @default(EBOOK)
  user      User     @relation(fields: [author_id], references: [id])
}

enum ManuscriptStatus {
  DRAFT
  SUBMITTED
  UNDER_REVIEW
  ACCEPTED
  REJECTED
}

enum ReviewStatus {
  PENDING
  ACCEPTED
  REJECTED
  SUBMITTED
}

enum Role {
  USER
  ADMIN
  REVIEWER
}

enum Pub_type {
  JOURNAL
  ARTICLE
  BOOK
  EBOOK
  AUDIOBOOK
}
