import GitHub from "next-auth/providers/github";
import NextAuth from "next-auth";
import Google from "next-auth/providers/google";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "@/prisma";


export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    GitHub,
    Google
    // Add your providers here
  ],
  secret: process.env.AUTH_SECRET,
  adapter: PrismaAdapter(prisma),
});
