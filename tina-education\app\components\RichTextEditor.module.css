/* Tiptap Editor Styles */
.editor {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.editor:focus-within {
  border-color: #6366f1;
  box-shadow: 0 0 0 1px #6366f1;
}

.toolbar {
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
  padding: 0.5rem 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.toolbarGroup {
  display: flex;
  gap: 0.25rem;
}

.toolbarButton {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.toolbarButton:hover {
  background-color: #f3f4f6;
}

.toolbarButton.active {
  background-color: #e0e7ff;
  border-color: #c7d2fe;
  color: #3730a3;
}

.toolbarButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.editorContent {
  min-height: 300px;
  background-color: white;
  padding: 1rem;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror {
  outline: none;
  min-height: 268px;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #6b7280; /* Changed from gray-400 to gray-500 for better legibility */
  pointer-events: none;
  height: 0;
}

.editorContent .ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.25;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.375;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.5;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror h4 {
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1.5;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror p {
  margin-bottom: 1rem;
  line-height: 1.625;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror ul,
.editorContent .ProseMirror ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror ul li {
  list-style-type: disc;
  margin-bottom: 0.25rem;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror ol li {
  list-style-type: decimal;
  margin-bottom: 0.25rem;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #374151; /* Changed from gray-500 to gray-700 for better legibility */
}

.editorContent .ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.875em;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror pre {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 1rem 0;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror pre code {
  background-color: transparent;
  padding: 0;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2rem 0;
}

.editorContent .ProseMirror strong {
  font-weight: 700;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror em {
  font-style: italic;
  color: #374151; /* gray-700 */
}

.editorContent .ProseMirror s {
  text-decoration: line-through;
  color: #374151; /* gray-700 */
}

.characterCount {
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  text-align: right;
}
