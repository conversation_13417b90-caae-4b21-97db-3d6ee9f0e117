"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-auth";
exports.ids = ["vendor-chunks/next-auth"];
exports.modules = {

/***/ "(rsc)/./node_modules/next-auth/index.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/index.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthError: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.AuthError),\n/* harmony export */   CredentialsSignin: () => (/* reexport safe */ _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__.CredentialsSignin),\n/* harmony export */   customFetch: () => (/* reexport safe */ _auth_core__WEBPACK_IMPORTED_MODULE_0__.customFetch),\n/* harmony export */   \"default\": () => (/* binding */ NextAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var _lib_env_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/env.js */ \"(rsc)/./node_modules/next-auth/lib/env.js\");\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/index.js */ \"(rsc)/./node_modules/next-auth/lib/index.js\");\n/* harmony import */ var _lib_actions_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/actions.js */ \"(rsc)/./node_modules/next-auth/lib/actions.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @auth/core/errors */ \"(rsc)/./node_modules/@auth/core/errors.js\");\n/**\n * _If you are looking to migrate from v4, visit the [Upgrade Guide (v5)](https://authjs.dev/getting-started/migrating-to-v5)._\n *\n * ## Installation\n *\n * ```bash npm2yarn\n * npm install next-auth@beta\n * ```\n *\n * ## Environment variable inference\n *\n * `NEXTAUTH_URL` and `NEXTAUTH_SECRET` have been inferred since v4.\n *\n * Since NextAuth.js v5 can also automatically infer environment variables that are prefixed with `AUTH_`.\n *\n * For example `AUTH_GITHUB_ID` and `AUTH_GITHUB_SECRET` will be used as the `clientId` and `clientSecret` options for the GitHub provider.\n *\n * :::tip\n * The environment variable name inferring has the following format for OAuth providers: `AUTH_{PROVIDER}_{ID|SECRET}`.\n *\n * `PROVIDER` is the uppercase snake case version of the provider's id, followed by either `ID` or `SECRET` respectively.\n * :::\n *\n * `AUTH_SECRET` and `AUTH_URL` are also aliased for `NEXTAUTH_SECRET` and `NEXTAUTH_URL` for consistency.\n *\n * To add social login to your app, the configuration becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth({ providers: [ GitHub ] })\n * ```\n *\n * And the `.env.local` file:\n *\n * ```sh title=\".env.local\"\n * AUTH_GITHUB_ID=...\n * AUTH_GITHUB_SECRET=...\n * AUTH_SECRET=...\n * ```\n *\n * :::tip\n * In production, `AUTH_SECRET` is a required environment variable - if not set, NextAuth.js will throw an error. See [MissingSecretError](https://authjs.dev/reference/core/errors#missingsecret) for more details.\n * :::\n *\n * If you need to override the default values for a provider, you can still call it as a function `GitHub({...})` as before.\n *\n * ## Lazy initialization\n * You can also initialize NextAuth.js lazily (previously known as advanced intialization), which allows you to access the request context in the configuration in some cases, like Route Handlers, Middleware, API Routes or `getServerSideProps`.\n * The above example becomes:\n *\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"next-auth/providers/github\"\n * export const { handlers, auth } = NextAuth(req => {\n *  if (req) {\n *   console.log(req) // do something with the request\n *  }\n *  return { providers: [ GitHub ] }\n * })\n * ```\n *\n * :::tip\n * This is useful if you want to customize the configuration based on the request, for example, to add a different provider in staging/dev environments.\n * :::\n *\n * @module next-auth\n */\n\n\n\n\n\n\n/**\n *  Initialize NextAuth.js.\n *\n *  @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth({ providers: [GitHub] })\n * ```\n *\n * Lazy initialization:\n *\n * @example\n * ```ts title=\"auth.ts\"\n * import NextAuth from \"next-auth\"\n * import GitHub from \"@auth/core/providers/github\"\n *\n * export const { handlers, auth } = NextAuth(async (req) => {\n *   console.log(req) // do something with the request\n *   return {\n *     providers: [GitHub],\n *   },\n * })\n * ```\n */\nfunction NextAuth(config) {\n    if (typeof config === \"function\") {\n        const httpHandler = async (req) => {\n            const _config = await config(req);\n            (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n            return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), _config);\n        };\n        return {\n            handlers: { GET: httpHandler, POST: httpHandler },\n            // @ts-expect-error\n            auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config, (c) => (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(c)),\n            signIn: async (provider, options, authorizationParams) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, _config);\n            },\n            signOut: async (options) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, _config);\n            },\n            unstable_update: async (data) => {\n                const _config = await config(undefined);\n                (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(_config);\n                return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, _config);\n            },\n        };\n    }\n    (0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(config);\n    const httpHandler = (req) => (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)((0,_lib_env_js__WEBPACK_IMPORTED_MODULE_1__.reqWithEnvURL)(req), config);\n    return {\n        handlers: { GET: httpHandler, POST: httpHandler },\n        // @ts-expect-error\n        auth: (0,_lib_index_js__WEBPACK_IMPORTED_MODULE_2__.initAuth)(config),\n        signIn: (provider, options, authorizationParams) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signIn)(provider, options, authorizationParams, config);\n        },\n        signOut: (options) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.signOut)(options, config);\n        },\n        unstable_update: (data) => {\n            return (0,_lib_actions_js__WEBPACK_IMPORTED_MODULE_3__.update)(data, config);\n        },\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/actions.js":
/*!***********************************************!*\
  !*** ./node_modules/next-auth/lib/actions.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   update: () => (/* binding */ update)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(rsc)/./node_modules/next/dist/api/navigation.react-server.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\nasync function signIn(provider, options = {}, authorizationParams, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    const { redirect: shouldRedirect = true, redirectTo, ...rest } = options instanceof FormData ? Object.fromEntries(options) : options;\n    const callbackUrl = redirectTo?.toString() ?? headers.get(\"Referer\") ?? \"/\";\n    const signInURL = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signin\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    if (!provider) {\n        signInURL.searchParams.append(\"callbackUrl\", callbackUrl);\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(signInURL.toString());\n        return signInURL.toString();\n    }\n    let url = `${signInURL}/${provider}?${new URLSearchParams(authorizationParams)}`;\n    let foundProvider = {};\n    for (const providerConfig of config.providers) {\n        const { options, ...defaults } = typeof providerConfig === \"function\" ? providerConfig() : providerConfig;\n        const id = options?.id ?? defaults.id;\n        if (id === provider) {\n            foundProvider = {\n                id,\n                type: options?.type ?? defaults.type,\n            };\n            break;\n        }\n    }\n    if (!foundProvider.id) {\n        const url = `${signInURL}?${new URLSearchParams({ callbackUrl })}`;\n        if (shouldRedirect)\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(url);\n        return url;\n    }\n    if (foundProvider.type === \"credentials\") {\n        url = url.replace(\"signin\", \"callback\");\n    }\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const body = new URLSearchParams({ ...rest, callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    const responseUrl = res instanceof Response ? res.headers.get(\"Location\") : res.redirect;\n    // NOTE: if for some unexpected reason the responseUrl is not set,\n    // we redirect to the original url\n    const redirectUrl = responseUrl ?? url;\n    if (shouldRedirect)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(redirectUrl);\n    return redirectUrl;\n}\nasync function signOut(options, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/x-www-form-urlencoded\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"signout\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const callbackUrl = options?.redirectTo ?? headers.get(\"Referer\") ?? \"/\";\n    const body = new URLSearchParams({ callbackUrl });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    if (options?.redirect ?? true)\n        return (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(res.redirect);\n    return res;\n}\nasync function update(data, config) {\n    const headers = new Headers(await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)());\n    headers.set(\"Content-Type\", \"application/json\");\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const body = JSON.stringify({ data });\n    const req = new Request(url, { method: \"POST\", headers, body });\n    const res = await (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(req, { ...config, raw: _auth_core__WEBPACK_IMPORTED_MODULE_0__.raw, skipCSRFCheck: _auth_core__WEBPACK_IMPORTED_MODULE_0__.skipCSRFCheck });\n    const cookieJar = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    for (const c of res?.cookies ?? [])\n        cookieJar.set(c.name, c.value, c.options);\n    return res.body;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/actions.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/env.js":
/*!*******************************************!*\
  !*** ./node_modules/next-auth/lib/env.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reqWithEnvURL: () => (/* binding */ reqWithEnvURL),\n/* harmony export */   setEnvDefaults: () => (/* binding */ setEnvDefaults)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\n/** If `NEXTAUTH_URL` or `AUTH_URL` is defined, override the request's URL. */\nfunction reqWithEnvURL(req) {\n    const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n    if (!url)\n        return req;\n    const { origin: envOrigin } = new URL(url);\n    const { href, origin } = req.nextUrl;\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextRequest(href.replace(origin, envOrigin), req);\n}\n/**\n * For backwards compatibility, `next-auth` checks for `NEXTAUTH_URL`\n * and the `basePath` by default is `/api/auth` instead of `/auth`\n * (which is the default for all other Auth.js integrations).\n *\n * For the same reason, `NEXTAUTH_SECRET` is also checked.\n */\nfunction setEnvDefaults(config) {\n    try {\n        config.secret ?? (config.secret = process.env.AUTH_SECRET ?? process.env.NEXTAUTH_SECRET);\n        const url = process.env.AUTH_URL ?? process.env.NEXTAUTH_URL;\n        if (!url)\n            return;\n        const { pathname } = new URL(url);\n        if (pathname === \"/\")\n            return;\n        config.basePath || (config.basePath = pathname);\n    }\n    catch {\n        // Catching and swallowing potential URL parsing errors, we'll fall\n        // back to `/api/auth` below.\n    }\n    finally {\n        config.basePath || (config.basePath = \"/api/auth\");\n        (0,_auth_core__WEBPACK_IMPORTED_MODULE_1__.setEnvDefaults)(process.env, config, true);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/env.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/lib/index.js":
/*!*********************************************!*\
  !*** ./node_modules/next-auth/lib/index.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initAuth: () => (/* binding */ initAuth)\n/* harmony export */ });\n/* harmony import */ var _auth_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core */ \"(rsc)/./node_modules/@auth/core/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _env_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./env.js */ \"(rsc)/./node_modules/next-auth/lib/env.js\");\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n// @ts-expect-error Next.js does not yet correctly use the `package.json#exports` field\n\n\nasync function getSession(headers, config) {\n    const url = (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.createActionURL)(\"session\", \n    // @ts-expect-error `x-forwarded-proto` is not nullable, next.js sets it by default\n    headers.get(\"x-forwarded-proto\"), headers, process.env, config);\n    const request = new Request(url, {\n        headers: { cookie: headers.get(\"cookie\") ?? \"\" },\n    });\n    return (0,_auth_core__WEBPACK_IMPORTED_MODULE_0__.Auth)(request, {\n        ...config,\n        callbacks: {\n            ...config.callbacks,\n            // Since we are server-side, we don't need to filter out the session data\n            // See https://authjs.dev/getting-started/migrating-to-v5#authenticating-server-side\n            // TODO: Taint the session data to prevent accidental leakage to the client\n            // https://react.dev/reference/react/experimental_taintObjectReference\n            async session(...args) {\n                const session = \n                // If the user defined a custom session callback, use that instead\n                (await config.callbacks?.session?.(...args)) ?? {\n                    ...args[0].session,\n                    expires: args[0].session.expires?.toISOString?.() ??\n                        args[0].session.expires,\n                };\n                const user = args[0].user ?? args[0].token;\n                return { user, ...session };\n            },\n        },\n    });\n}\nfunction isReqWrapper(arg) {\n    return typeof arg === \"function\";\n}\nfunction initAuth(config, onLazyLoad // To set the default env vars\n) {\n    if (typeof config === \"function\") {\n        return async (...args) => {\n            if (!args.length) {\n                // React Server Components\n                const _headers = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n                const _config = await config(undefined); // Review: Should we pass headers() here instead?\n                onLazyLoad?.(_config);\n                return getSession(_headers, _config).then((r) => r.json());\n            }\n            if (args[0] instanceof Request) {\n                // middleware.ts inline\n                // export { auth as default } from \"auth\"\n                const req = args[0];\n                const ev = args[1];\n                const _config = await config(req);\n                onLazyLoad?.(_config);\n                // args[0] is supposed to be NextRequest but the instanceof check is failing.\n                return handleAuth([req, ev], _config);\n            }\n            if (isReqWrapper(args[0])) {\n                // middleware.ts wrapper/route.ts\n                // import { auth } from \"auth\"\n                // export default auth((req) => { console.log(req.auth) }})\n                const userMiddlewareOrRoute = args[0];\n                return async (...args) => {\n                    const _config = await config(args[0]);\n                    onLazyLoad?.(_config);\n                    return handleAuth(args, _config, userMiddlewareOrRoute);\n                };\n            }\n            // API Routes, getServerSideProps\n            const request = \"req\" in args[0] ? args[0].req : args[0];\n            const response = \"res\" in args[0] ? args[0].res : args[1];\n            const _config = await config(request);\n            onLazyLoad?.(_config);\n            // @ts-expect-error -- request is NextRequest\n            return getSession(new Headers(request.headers), _config).then(async (authResponse) => {\n                const auth = await authResponse.json();\n                for (const cookie of authResponse.headers.getSetCookie())\n                    if (\"headers\" in response)\n                        response.headers.append(\"set-cookie\", cookie);\n                    else\n                        response.appendHeader(\"set-cookie\", cookie);\n                return auth;\n            });\n        };\n    }\n    return (...args) => {\n        if (!args.length) {\n            // React Server Components\n            return Promise.resolve((0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)()).then((h) => getSession(h, config).then((r) => r.json()));\n        }\n        if (args[0] instanceof Request) {\n            // middleware.ts inline\n            // export { auth as default } from \"auth\"\n            const req = args[0];\n            const ev = args[1];\n            return handleAuth([req, ev], config);\n        }\n        if (isReqWrapper(args[0])) {\n            // middleware.ts wrapper/route.ts\n            // import { auth } from \"auth\"\n            // export default auth((req) => { console.log(req.auth) }})\n            const userMiddlewareOrRoute = args[0];\n            return async (...args) => {\n                return handleAuth(args, config, userMiddlewareOrRoute).then((res) => {\n                    return res;\n                });\n            };\n        }\n        // API Routes, getServerSideProps\n        const request = \"req\" in args[0] ? args[0].req : args[0];\n        const response = \"res\" in args[0] ? args[0].res : args[1];\n        return getSession(\n        // @ts-expect-error\n        new Headers(request.headers), config).then(async (authResponse) => {\n            const auth = await authResponse.json();\n            for (const cookie of authResponse.headers.getSetCookie())\n                if (\"headers\" in response)\n                    response.headers.append(\"set-cookie\", cookie);\n                else\n                    response.appendHeader(\"set-cookie\", cookie);\n            return auth;\n        });\n    };\n}\nasync function handleAuth(args, config, userMiddlewareOrRoute) {\n    const request = (0,_env_js__WEBPACK_IMPORTED_MODULE_3__.reqWithEnvURL)(args[0]);\n    const sessionResponse = await getSession(request.headers, config);\n    const auth = await sessionResponse.json();\n    let authorized = true;\n    if (config.callbacks?.authorized) {\n        authorized = await config.callbacks.authorized({ request, auth });\n    }\n    let response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next?.();\n    if (authorized instanceof Response) {\n        // User returned a custom response, like redirecting to a page or 401, respect it\n        response = authorized;\n        const redirect = authorized.headers.get(\"Location\");\n        const { pathname } = request.nextUrl;\n        // If the user is redirecting to the same NextAuth.js action path as the current request,\n        // don't allow the redirect to prevent an infinite loop\n        if (redirect &&\n            isSameAuthAction(pathname, new URL(redirect).pathname, config)) {\n            authorized = true;\n        }\n    }\n    else if (userMiddlewareOrRoute) {\n        // Execute user's middleware/handler with the augmented request\n        const augmentedReq = request;\n        augmentedReq.auth = auth;\n        response =\n            (await userMiddlewareOrRoute(augmentedReq, args[1])) ??\n                next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.next();\n    }\n    else if (!authorized) {\n        const signInPage = config.pages?.signIn ?? `${config.basePath}/signin`;\n        if (request.nextUrl.pathname !== signInPage) {\n            // Redirect to signin page by default if not authorized\n            const signInUrl = request.nextUrl.clone();\n            signInUrl.pathname = signInPage;\n            signInUrl.searchParams.set(\"callbackUrl\", request.nextUrl.href);\n            response = next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.redirect(signInUrl);\n        }\n    }\n    const finalResponse = new Response(response?.body, response);\n    // Preserve cookies from the session response\n    for (const cookie of sessionResponse.headers.getSetCookie())\n        finalResponse.headers.append(\"set-cookie\", cookie);\n    return finalResponse;\n}\nfunction isSameAuthAction(requestPath, redirectPath, config) {\n    const action = redirectPath.replace(`${requestPath}/`, \"\");\n    const pages = Object.values(config.pages ?? {});\n    return ((actions.has(action) || pages.includes(redirectPath)) &&\n        redirectPath === requestPath);\n}\nconst actions = new Set([\n    \"providers\",\n    \"session\",\n    \"csrf\",\n    \"signin\",\n    \"signout\",\n    \"callback\",\n    \"verify-request\",\n    \"error\",\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/credentials.js":
/*!*********************************************************!*\
  !*** ./node_modules/next-auth/providers/credentials.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_credentials__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/credentials */ \"(rsc)/./node_modules/@auth/core/providers/credentials.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9jcmVkZW50aWFscy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFpRDtBQUNVIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXHByb3ZpZGVyc1xcY3JlZGVudGlhbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2NyZWRlbnRpYWxzXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2NyZWRlbnRpYWxzXCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/credentials.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/next-auth/providers/google.js":
/*!****************************************************!*\
  !*** ./node_modules/next-auth/providers/google.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _auth_core_providers_google__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @auth/core/providers/google */ \"(rsc)/./node_modules/@auth/core/providers/google.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL3Byb3ZpZGVycy9nb29nbGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEM7QUFDVSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiaWNoZVxcVGluYSBFZHVjYXRpb24ub3JnXFxSZXZpZXcgUmVxdWVzdCBiYWNrZW5kXFxBUlJTLU5leHRKU1xcdGluYS1lZHVjYXRpb25cXG5vZGVfbW9kdWxlc1xcbmV4dC1hdXRoXFxwcm92aWRlcnNcXGdvb2dsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiQGF1dGgvY29yZS9wcm92aWRlcnMvZ29vZ2xlXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIkBhdXRoL2NvcmUvcHJvdmlkZXJzL2dvb2dsZVwiO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next-auth/providers/google.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/lib/client.js":
/*!**********************************************!*\
  !*** ./node_modules/next-auth/lib/client.js ***!
  \**********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClientSessionError: () => (/* binding */ ClientSessionError),\n/* harmony export */   apiBaseUrl: () => (/* binding */ apiBaseUrl),\n/* harmony export */   fetchData: () => (/* binding */ fetchData),\n/* harmony export */   now: () => (/* binding */ now),\n/* harmony export */   parseUrl: () => (/* binding */ parseUrl),\n/* harmony export */   useOnline: () => (/* binding */ useOnline)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/core/errors */ \"(ssr)/./node_modules/@auth/core/errors.js\");\n/* __next_internal_client_entry_do_not_use__ ClientSessionError,fetchData,apiBaseUrl,useOnline,now,parseUrl auto */ \n\n/** @todo */ class ClientFetchError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n/** @todo */ class ClientSessionError extends _auth_core_errors__WEBPACK_IMPORTED_MODULE_1__.AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */ async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...req?.headers?.cookie ? {\n                    cookie: req.headers.cookie\n                } : {}\n            }\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok) throw data;\n        return data;\n    } catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */ function apiBaseUrl(__NEXTAUTH) {\n    if (true) {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */ function useOnline() {\n    const [isOnline, setIsOnline] = react__WEBPACK_IMPORTED_MODULE_0__.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = ()=>setIsOnline(true);\n    const setOffline = ()=>setIsOnline(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useOnline.useEffect\": ()=>{\n            window.addEventListener(\"online\", setOnline);\n            window.addEventListener(\"offline\", setOffline);\n            return ({\n                \"useOnline.useEffect\": ()=>{\n                    window.removeEventListener(\"online\", setOnline);\n                    window.removeEventListener(\"offline\", setOffline);\n                }\n            })[\"useOnline.useEffect\"];\n        }\n    }[\"useOnline.useEffect\"], []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */ function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */ function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)// Remove trailing slash\n    .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: ()=>base\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC1hdXRoL2xpYi9jbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7b0hBQytCO0FBQ2U7QUFDOUMsVUFBVSxHQUNWLE1BQU1FLHlCQUF5QkQsd0RBQVNBO0FBQ3hDO0FBQ0EsVUFBVSxHQUNILE1BQU1FLDJCQUEyQkYsd0RBQVNBO0FBQ2pEO0FBQ0EsNkRBQTZEO0FBQzdEOzs7Ozs7O0NBT0MsR0FDTSxlQUFlRyxVQUFVQyxJQUFJLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFQyxNQUFNLENBQUMsQ0FBQztJQUM5RCxNQUFNQyxNQUFNLEdBQUdDLFdBQVdKLFlBQVksQ0FBQyxFQUFFRCxNQUFNO0lBQy9DLElBQUk7UUFDQSxNQUFNTSxVQUFVO1lBQ1pDLFNBQVM7Z0JBQ0wsZ0JBQWdCO2dCQUNoQixHQUFJSixLQUFLSSxTQUFTQyxTQUFTO29CQUFFQSxRQUFRTCxJQUFJSSxPQUFPLENBQUNDLE1BQU07Z0JBQUMsSUFBSSxDQUFDLENBQUM7WUFDbEU7UUFDSjtRQUNBLElBQUlMLEtBQUtNLE1BQU07WUFDWEgsUUFBUUcsSUFBSSxHQUFHQyxLQUFLQyxTQUFTLENBQUNSLElBQUlNLElBQUk7WUFDdENILFFBQVFNLE1BQU0sR0FBRztRQUNyQjtRQUNBLE1BQU1DLE1BQU0sTUFBTUMsTUFBTVYsS0FBS0U7UUFDN0IsTUFBTVMsT0FBTyxNQUFNRixJQUFJRyxJQUFJO1FBQzNCLElBQUksQ0FBQ0gsSUFBSUksRUFBRSxFQUNQLE1BQU1GO1FBQ1YsT0FBT0E7SUFDWCxFQUNBLE9BQU9HLE9BQU87UUFDVmhCLE9BQU9nQixLQUFLLENBQUMsSUFBSXJCLGlCQUFpQnFCLE1BQU1DLE9BQU8sRUFBRUQ7UUFDakQsT0FBTztJQUNYO0FBQ0o7QUFDQSxjQUFjLEdBQ1AsU0FBU2IsV0FBV0osVUFBVTtJQUNqQyxJQUFJLElBQTZCLEVBQUU7UUFDL0IsK0NBQStDO1FBQy9DLE9BQU8sR0FBR0EsV0FBV21CLGFBQWEsR0FBR25CLFdBQVdvQixjQUFjLEVBQUU7SUFDcEU7SUFDQSwrQ0FBK0M7SUFDL0MsT0FBT3BCLFdBQVdxQixRQUFRO0FBQzlCO0FBQ0EsZUFBZSxHQUNSLFNBQVNDO0lBQ1osTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUc5QiwyQ0FBYyxDQUFDLE9BQU9nQyxjQUFjLGNBQWNBLFVBQVVDLE1BQU0sR0FBRztJQUNyRyxNQUFNQyxZQUFZLElBQU1KLFlBQVk7SUFDcEMsTUFBTUssYUFBYSxJQUFNTCxZQUFZO0lBQ3JDOUIsNENBQWU7K0JBQUM7WUFDWnFDLE9BQU9DLGdCQUFnQixDQUFDLFVBQVVKO1lBQ2xDRyxPQUFPQyxnQkFBZ0IsQ0FBQyxXQUFXSDtZQUNuQzt1Q0FBTztvQkFDSEUsT0FBT0UsbUJBQW1CLENBQUMsVUFBVUw7b0JBQ3JDRyxPQUFPRSxtQkFBbUIsQ0FBQyxXQUFXSjtnQkFDMUM7O1FBQ0o7OEJBQUcsRUFBRTtJQUNMLE9BQU9OO0FBQ1g7QUFDQTs7O0NBR0MsR0FDTSxTQUFTVztJQUNaLE9BQU9DLEtBQUtDLEtBQUssQ0FBQ0MsS0FBS0gsR0FBRyxLQUFLO0FBQ25DO0FBQ0E7OztDQUdDLEdBQ00sU0FBU0ksU0FBU25DLEdBQUc7SUFDeEIsTUFBTW9DLGFBQWEsSUFBSUMsSUFBSTtJQUMzQixJQUFJckMsT0FBTyxDQUFDQSxJQUFJc0MsVUFBVSxDQUFDLFNBQVM7UUFDaEN0QyxNQUFNLENBQUMsUUFBUSxFQUFFQSxLQUFLO0lBQzFCO0lBQ0EsTUFBTXVDLE9BQU8sSUFBSUYsSUFBSXJDLE9BQU9vQztJQUM1QixNQUFNeEMsT0FBTyxDQUFDMkMsS0FBS0MsUUFBUSxLQUFLLE1BQU1KLFdBQVdJLFFBQVEsR0FBR0QsS0FBS0MsUUFBUSxDQUNyRSx3QkFBd0I7S0FDdkJDLE9BQU8sQ0FBQyxPQUFPO0lBQ3BCLE1BQU1DLE9BQU8sR0FBR0gsS0FBS0ksTUFBTSxHQUFHL0MsTUFBTTtJQUNwQyxPQUFPO1FBQ0grQyxRQUFRSixLQUFLSSxNQUFNO1FBQ25CQyxNQUFNTCxLQUFLSyxJQUFJO1FBQ2ZoRDtRQUNBOEM7UUFDQUcsVUFBVSxJQUFNSDtJQUNwQjtBQUNKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxcbm9kZV9tb2R1bGVzXFxuZXh0LWF1dGhcXGxpYlxcY2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBBdXRoRXJyb3IgfSBmcm9tIFwiQGF1dGgvY29yZS9lcnJvcnNcIjtcbi8qKiBAdG9kbyAqL1xuY2xhc3MgQ2xpZW50RmV0Y2hFcnJvciBleHRlbmRzIEF1dGhFcnJvciB7XG59XG4vKiogQHRvZG8gKi9cbmV4cG9ydCBjbGFzcyBDbGllbnRTZXNzaW9uRXJyb3IgZXh0ZW5kcyBBdXRoRXJyb3Ige1xufVxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIEludGVybmFsIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuLyoqXG4gKiBJZiBwYXNzZWQgJ2FwcENvbnRleHQnIHZpYSBnZXRJbml0aWFsUHJvcHMoKSBpbiBfYXBwLmpzXG4gKiB0aGVuIGdldCB0aGUgcmVxIG9iamVjdCBmcm9tIGN0eCBhbmQgdXNlIHRoYXQgZm9yIHRoZVxuICogcmVxIHZhbHVlIHRvIGFsbG93IGBmZXRjaERhdGFgIHRvXG4gKiB3b3JrIHNlZW1sZXNzbHkgaW4gZ2V0SW5pdGlhbFByb3BzKCkgb24gc2VydmVyIHNpZGVcbiAqIHBhZ2VzICphbmQqIGluIF9hcHAuanMuXG4gKiBAaW50ZXJuYWxcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGZldGNoRGF0YShwYXRoLCBfX05FWFRBVVRILCBsb2dnZXIsIHJlcSA9IHt9KSB7XG4gICAgY29uc3QgdXJsID0gYCR7YXBpQmFzZVVybChfX05FWFRBVVRIKX0vJHtwYXRofWA7XG4gICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcbiAgICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgICAgICAgICAuLi4ocmVxPy5oZWFkZXJzPy5jb29raWUgPyB7IGNvb2tpZTogcmVxLmhlYWRlcnMuY29va2llIH0gOiB7fSksXG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgICAgICBpZiAocmVxPy5ib2R5KSB7XG4gICAgICAgICAgICBvcHRpb25zLmJvZHkgPSBKU09OLnN0cmluZ2lmeShyZXEuYm9keSk7XG4gICAgICAgICAgICBvcHRpb25zLm1ldGhvZCA9IFwiUE9TVFwiO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoKHVybCwgb3B0aW9ucyk7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXMuanNvbigpO1xuICAgICAgICBpZiAoIXJlcy5vaylcbiAgICAgICAgICAgIHRocm93IGRhdGE7XG4gICAgICAgIHJldHVybiBkYXRhO1xuICAgIH1cbiAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgbG9nZ2VyLmVycm9yKG5ldyBDbGllbnRGZXRjaEVycm9yKGVycm9yLm1lc3NhZ2UsIGVycm9yKSk7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn1cbi8qKiBAaW50ZXJuYWwgKi9cbmV4cG9ydCBmdW5jdGlvbiBhcGlCYXNlVXJsKF9fTkVYVEFVVEgpIHtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAvLyBSZXR1cm4gYWJzb2x1dGUgcGF0aCB3aGVuIGNhbGxlZCBzZXJ2ZXIgc2lkZVxuICAgICAgICByZXR1cm4gYCR7X19ORVhUQVVUSC5iYXNlVXJsU2VydmVyfSR7X19ORVhUQVVUSC5iYXNlUGF0aFNlcnZlcn1gO1xuICAgIH1cbiAgICAvLyBSZXR1cm4gcmVsYXRpdmUgcGF0aCB3aGVuIGNhbGxlZCBjbGllbnQgc2lkZVxuICAgIHJldHVybiBfX05FWFRBVVRILmJhc2VQYXRoO1xufVxuLyoqIEBpbnRlcm5hbCAgKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VPbmxpbmUoKSB7XG4gICAgY29uc3QgW2lzT25saW5lLCBzZXRJc09ubGluZV0gPSBSZWFjdC51c2VTdGF0ZSh0eXBlb2YgbmF2aWdhdG9yICE9PSBcInVuZGVmaW5lZFwiID8gbmF2aWdhdG9yLm9uTGluZSA6IGZhbHNlKTtcbiAgICBjb25zdCBzZXRPbmxpbmUgPSAoKSA9PiBzZXRJc09ubGluZSh0cnVlKTtcbiAgICBjb25zdCBzZXRPZmZsaW5lID0gKCkgPT4gc2V0SXNPbmxpbmUoZmFsc2UpO1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwib25saW5lXCIsIHNldE9ubGluZSk7XG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKFwib2ZmbGluZVwiLCBzZXRPZmZsaW5lKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKFwib25saW5lXCIsIHNldE9ubGluZSk7XG4gICAgICAgICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcIm9mZmxpbmVcIiwgc2V0T2ZmbGluZSk7XG4gICAgICAgIH07XG4gICAgfSwgW10pO1xuICAgIHJldHVybiBpc09ubGluZTtcbn1cbi8qKlxuICogUmV0dXJucyB0aGUgbnVtYmVyIG9mIHNlY29uZHMgZWxhcHNlZCBzaW5jZSBKYW51YXJ5IDEsIDE5NzAgMDA6MDA6MDAgVVRDLlxuICogQGludGVybmFsXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBub3coKSB7XG4gICAgcmV0dXJuIE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xufVxuLyoqXG4gKiBSZXR1cm5zIGFuIGBVUkxgIGxpa2Ugb2JqZWN0IHRvIG1ha2UgcmVxdWVzdHMvcmVkaXJlY3RzIGZyb20gc2VydmVyLXNpZGVcbiAqIEBpbnRlcm5hbFxuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyc2VVcmwodXJsKSB7XG4gICAgY29uc3QgZGVmYXVsdFVybCA9IG5ldyBVUkwoXCJodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpL2F1dGhcIik7XG4gICAgaWYgKHVybCAmJiAhdXJsLnN0YXJ0c1dpdGgoXCJodHRwXCIpKSB7XG4gICAgICAgIHVybCA9IGBodHRwczovLyR7dXJsfWA7XG4gICAgfVxuICAgIGNvbnN0IF91cmwgPSBuZXcgVVJMKHVybCB8fCBkZWZhdWx0VXJsKTtcbiAgICBjb25zdCBwYXRoID0gKF91cmwucGF0aG5hbWUgPT09IFwiL1wiID8gZGVmYXVsdFVybC5wYXRobmFtZSA6IF91cmwucGF0aG5hbWUpXG4gICAgICAgIC8vIFJlbW92ZSB0cmFpbGluZyBzbGFzaFxuICAgICAgICAucmVwbGFjZSgvXFwvJC8sIFwiXCIpO1xuICAgIGNvbnN0IGJhc2UgPSBgJHtfdXJsLm9yaWdpbn0ke3BhdGh9YDtcbiAgICByZXR1cm4ge1xuICAgICAgICBvcmlnaW46IF91cmwub3JpZ2luLFxuICAgICAgICBob3N0OiBfdXJsLmhvc3QsXG4gICAgICAgIHBhdGgsXG4gICAgICAgIGJhc2UsXG4gICAgICAgIHRvU3RyaW5nOiAoKSA9PiBiYXNlLFxuICAgIH07XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJBdXRoRXJyb3IiLCJDbGllbnRGZXRjaEVycm9yIiwiQ2xpZW50U2Vzc2lvbkVycm9yIiwiZmV0Y2hEYXRhIiwicGF0aCIsIl9fTkVYVEFVVEgiLCJsb2dnZXIiLCJyZXEiLCJ1cmwiLCJhcGlCYXNlVXJsIiwib3B0aW9ucyIsImhlYWRlcnMiLCJjb29raWUiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsIm1ldGhvZCIsInJlcyIsImZldGNoIiwiZGF0YSIsImpzb24iLCJvayIsImVycm9yIiwibWVzc2FnZSIsImJhc2VVcmxTZXJ2ZXIiLCJiYXNlUGF0aFNlcnZlciIsImJhc2VQYXRoIiwidXNlT25saW5lIiwiaXNPbmxpbmUiLCJzZXRJc09ubGluZSIsInVzZVN0YXRlIiwibmF2aWdhdG9yIiwib25MaW5lIiwic2V0T25saW5lIiwic2V0T2ZmbGluZSIsInVzZUVmZmVjdCIsIndpbmRvdyIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwibm93IiwiTWF0aCIsImZsb29yIiwiRGF0ZSIsInBhcnNlVXJsIiwiZGVmYXVsdFVybCIsIlVSTCIsInN0YXJ0c1dpdGgiLCJfdXJsIiwicGF0aG5hbWUiLCJyZXBsYWNlIiwiYmFzZSIsIm9yaWdpbiIsImhvc3QiLCJ0b1N0cmluZyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/lib/client.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/next-auth/react.js":
/*!*****************************************!*\
  !*** ./node_modules/next-auth/react.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionContext: () => (/* binding */ SessionContext),\n/* harmony export */   SessionProvider: () => (/* binding */ SessionProvider),\n/* harmony export */   __NEXTAUTH: () => (/* binding */ __NEXTAUTH),\n/* harmony export */   getCsrfToken: () => (/* binding */ getCsrfToken),\n/* harmony export */   getProviders: () => (/* binding */ getProviders),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   useSession: () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _lib_client_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/client.js */ \"(ssr)/./node_modules/next-auth/lib/client.js\");\n/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */ /* __next_internal_client_entry_do_not_use__ __NEXTAUTH,SessionContext,useSession,getSession,getCsrfToken,getProviders,signIn,signOut,SessionProvider auto */ \n\n\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nconst __NEXTAUTH = {\n    baseUrl: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePathServer: (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.parseUrl)(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: ()=>{}\n};\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: ()=>{},\n            addEventListener: ()=>{},\n            removeEventListener: ()=>{}\n        };\n    }\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn\n};\nconst SessionContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SessionContext);\n    if (!value && \"development\" !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"useSession.useEffect\": ()=>{\n            if (requiredAndNotLoading) {\n                const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                    error: \"SessionRequired\",\n                    callbackUrl: window.location.href\n                })}`;\n                if (onUnauthenticated) onUnauthenticated();\n                else window.location.href = url;\n            }\n        }\n    }[\"useSession.useEffect\"], [\n        requiredAndNotLoading,\n        onUnauthenticated\n    ]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\"\n        };\n    }\n    return value;\n}\nasync function getSession(params) {\n    const session = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        const broadcastChannel = getNewBroadcastChannel();\n        broadcastChannel.postMessage({\n            event: \"session\",\n            data: {\n                trigger: \"getSession\"\n            }\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */ async function getCsrfToken() {\n    const response = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\nasync function getProviders() {\n    return (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"providers\", __NEXTAUTH, logger);\n}\nasync function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = `${baseUrl}/error`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo\n        })}`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            `Provider id \"${provider}\" refers to a WebAuthn provider.`,\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.'\n        ].join(\"\\n\"));\n    }\n    const signInUrl = `${baseUrl}/${providerType === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({\n            event: \"storage\"\n        });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url\n    };\n}\nasync function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href } = options ?? {};\n    const baseUrl = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.apiBaseUrl)(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\"\n        },\n        body: new URLSearchParams({\n            csrfToken,\n            callbackUrl: redirectTo\n        })\n    });\n    const data = await res.json();\n    broadcast().postMessage({\n        event: \"session\",\n        data: {\n            trigger: \"signout\"\n        }\n    });\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\")) window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({\n        event: \"storage\"\n    });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */ function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath) __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */ const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */ __NEXTAUTH._lastSync = hasInitialSession ? (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() : 0;\n    const [session, setSession] = react__WEBPACK_IMPORTED_MODULE_1__.useState({\n        \"SessionProvider.useState\": ()=>{\n            if (hasInitialSession) __NEXTAUTH._session = props.session;\n            return props.session;\n        }\n    }[\"SessionProvider.useState\"]);\n    /** If session was passed, initialize as not loading */ const [loading, setLoading] = react__WEBPACK_IMPORTED_MODULE_1__.useState(!hasInitialSession);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            __NEXTAUTH._getSession = ({\n                \"SessionProvider.useEffect\": async ({ event } = {})=>{\n                    try {\n                        const storageEvent = event === \"storage\";\n                        // We should always update if we don't have a client session yet\n                        // or if there are events from other tabs/windows\n                        if (storageEvent || __NEXTAUTH._session === undefined) {\n                            __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                            __NEXTAUTH._session = await getSession({\n                                broadcast: !storageEvent\n                            });\n                            setSession(__NEXTAUTH._session);\n                            return;\n                        }\n                        if (// If there is no time defined for when a session should be considered\n                        // stale, then it's okay to use the value we have until an event is\n                        // triggered which updates it\n                        !event || // If the client doesn't have a session then we don't need to call\n                        // the server to check if it does (if they have signed in via another\n                        // tab or window that will come through as a \"stroage\" event\n                        // event anyway)\n                        __NEXTAUTH._session === null || // Bail out early if the client session is not stale yet\n                        (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)() < __NEXTAUTH._lastSync) {\n                            return;\n                        }\n                        // An event or session staleness occurred, update the client session.\n                        __NEXTAUTH._lastSync = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.now)();\n                        __NEXTAUTH._session = await getSession();\n                        setSession(__NEXTAUTH._session);\n                    } catch (error) {\n                        logger.error(new _lib_client_js__WEBPACK_IMPORTED_MODULE_2__.ClientSessionError(error.message, error));\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            })[\"SessionProvider.useEffect\"];\n            __NEXTAUTH._getSession();\n            return ({\n                \"SessionProvider.useEffect\": ()=>{\n                    __NEXTAUTH._lastSync = 0;\n                    __NEXTAUTH._session = undefined;\n                    __NEXTAUTH._getSession = ({\n                        \"SessionProvider.useEffect\": ()=>{}\n                    })[\"SessionProvider.useEffect\"];\n                }\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const handle = {\n                \"SessionProvider.useEffect.handle\": ()=>__NEXTAUTH._getSession({\n                        event: \"storage\"\n                    })\n            }[\"SessionProvider.useEffect.handle\"];\n            // Listen for storage events and update session if event fired from\n            // another window (but suppress firing another event to avoid a loop)\n            // Fetch new session data but tell it to not to fire another event to\n            // avoid an infinite loop.\n            // Note: We could pass session data through and do something like\n            // `setData(message.data)` but that can cause problems depending\n            // on how the session object is being used in the client; it is\n            // more robust to have each window/tab fetch it's own copy of the\n            // session object rather than share it across instances.\n            broadcast().addEventListener(\"message\", handle);\n            return ({\n                \"SessionProvider.useEffect\": ()=>broadcast().removeEventListener(\"message\", handle)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            const { refetchOnWindowFocus = true } = props;\n            // Listen for when the page is visible, if the user switches tabs\n            // and makes our tab visible again, re-fetch the session, but only if\n            // this feature is not disabled.\n            const visibilityHandler = {\n                \"SessionProvider.useEffect.visibilityHandler\": ()=>{\n                    if (refetchOnWindowFocus && document.visibilityState === \"visible\") __NEXTAUTH._getSession({\n                        event: \"visibilitychange\"\n                    });\n                }\n            }[\"SessionProvider.useEffect.visibilityHandler\"];\n            document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n            return ({\n                \"SessionProvider.useEffect\": ()=>document.removeEventListener(\"visibilitychange\", visibilityHandler, false)\n            })[\"SessionProvider.useEffect\"];\n        }\n    }[\"SessionProvider.useEffect\"], [\n        props.refetchOnWindowFocus\n    ]);\n    const isOnline = (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.useOnline)();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SessionProvider.useEffect\": ()=>{\n            if (refetchInterval && shouldRefetch) {\n                const refetchIntervalTimer = setInterval({\n                    \"SessionProvider.useEffect.refetchIntervalTimer\": ()=>{\n                        if (__NEXTAUTH._session) {\n                            __NEXTAUTH._getSession({\n                                event: \"poll\"\n                            });\n                        }\n                    }\n                }[\"SessionProvider.useEffect.refetchIntervalTimer\"], refetchInterval * 1000);\n                return ({\n                    \"SessionProvider.useEffect\": ()=>clearInterval(refetchIntervalTimer)\n                })[\"SessionProvider.useEffect\"];\n            }\n        }\n    }[\"SessionProvider.useEffect\"], [\n        refetchInterval,\n        shouldRefetch\n    ]);\n    const value = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SessionProvider.useMemo[value]\": ()=>({\n                data: session,\n                status: loading ? \"loading\" : session ? \"authenticated\" : \"unauthenticated\",\n                async update (data) {\n                    if (loading) return;\n                    setLoading(true);\n                    const newSession = await (0,_lib_client_js__WEBPACK_IMPORTED_MODULE_2__.fetchData)(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\" ? undefined : {\n                        body: {\n                            csrfToken: await getCsrfToken(),\n                            data\n                        }\n                    });\n                    setLoading(false);\n                    if (newSession) {\n                        setSession(newSession);\n                        broadcast().postMessage({\n                            event: \"session\",\n                            data: {\n                                trigger: \"getSession\"\n                            }\n                        });\n                    }\n                    return newSession;\n                }\n            })\n    }[\"SessionProvider.useMemo[value]\"], [\n        session,\n        loading\n    ]);\n    return(// @ts-expect-error\n    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SessionContext.Provider, {\n        value: value,\n        children: children\n    }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-auth/react.js\n");

/***/ })

};
;