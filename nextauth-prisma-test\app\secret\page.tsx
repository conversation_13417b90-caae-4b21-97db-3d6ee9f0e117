import { auth, signIn, signOut } from "@/auth";

export default async function Secret() {
  const session = await auth();
  console.log("Session:", session);
  const user = session?.user;

  if (user) {
    return (
      <div>
        <h1 className="text-2xl text-green-700 font-bold">Welcome to the Secret Page</h1>
            <p className="text-lg">Welcome, {user.name}</p>

            <p className="text-lg">You can now sign out</p>
            <form
              action={async () => {
                "use server";
                await signOut();
              }}
            >
              <button className="bg-red-500 text-white px-4 py-2 rounded" type="submit">Sign out</button>
            </form>
      </div>
    );
  }
  return (
    <div>
          <h1 className="text-2xl text-red-700 font-bold">You are not authenticated</h1>
          <p className="text-lg">Please sign in to access this page.</p>
            <form
                action={async () => {
                "use server";
                await signIn("google");
                }}
            >
              <button className="bg-blue-500 text-white px-4 py-2 rounded" type="submit">Sign in with Google</button>
            </form>
    </div>
  );
}