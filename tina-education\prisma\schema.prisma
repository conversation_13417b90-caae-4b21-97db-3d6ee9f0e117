generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String          @id @default(cuid())
  createdAt     DateTime        @default(now())
  email         String          @unique
  updatedAt     DateTime        @updatedAt
  image         String?
  emailVerified DateTime?
  role          Role            @default(REVIEWER)
  name          String?
  accounts      Account[]
  Authenticator Authenticator[]
  manuscripts   Manuscript[]
  Notification  Notification[]
  publications  Publication[]
  review        Review[]
  reviewMessages ReviewMessage[]
  sessions      Session[]
}

model Manuscript {
  id        String           @id @default(cuid())
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
  title     String
  abstract  String
  content   String
  status    ManuscriptStatus @default(DRAFT)
  type      Pub_type         @default(ARTICLE)
  keywords  String
  author_id String
  pdfFile   String           // Changed from Bytes to String to store Vercel Blob URL
  uploadedFile String?        // URL for user-uploaded PDF/Word files
  uploadedFileName String?    // Original filename of uploaded file
  user      User             @relation(fields: [author_id], references: [id])
  reviews   Review[]
}

model Review {
  id                    String             @id @default(cuid())
  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt
  content               String
  manuscript_id         String
  reviewer_id           String
  feedback              String?
  score                 Int?
  status                ReviewStatus       @default(PENDING)

  // Detailed review fields
  contentEvaluation     String?
  styleEvaluation       String?
  strengths             String?
  weaknesses            String?
  recommendation        ReviewRecommendation?
  confidentialComments  String?
  publicComments        String?
  overallRating         Int?               // 1-5 scale

  // Review progress tracking
  progressPercentage    Int                @default(0)
  timeSpent             Int                @default(0) // in minutes

  // Revision tracking
  revisionRound         Int                @default(1)
  previousReviewId      String?

  manuscript            Manuscript         @relation(fields: [manuscript_id], references: [id])
  user                  User               @relation(fields: [reviewer_id], references: [id])
  messages              ReviewMessage[]
}

model ReviewMessage {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  content   String
  sender    MessageSender
  reviewId  String
  userId    String
  review    Review   @relation(fields: [reviewId], references: [id])
  user      User     @relation(fields: [userId], references: [id])
}

model Publication {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  title     String
  abstract  String?
  content   String?
  keywords  String?
  cover     String?
  author_id String
  type      Pub_type @default(EBOOK)
  user      User     @relation(fields: [author_id], references: [id])
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
}

model Authenticator {
  credentialID         String  @unique
  userId               String
  providerAccountId    String
  credentialPublicKey  String
  counter              Int
  credentialDeviceType String
  credentialBackedUp   Boolean
  transports           String?
  user                 User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([userId, credentialID])
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  title     String
  message   String
  isRead    Boolean  @default(false)
  type      String
  relatedId String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User     @relation(fields: [userId], references: [id])
}

enum ManuscriptStatus {
  DRAFT
  SUBMITTED
  UNDER_REVIEW
  ACCEPTED
  REJECTED
}

enum ReviewStatus {
  PENDING
  ACCEPTED
  DECLINED
  IN_REVIEW
  REVIEW_SUBMITTED
}

enum ReviewRecommendation {
  ACCEPT
  MINOR_REVISIONS
  MAJOR_REVISIONS
  REJECT
}

enum MessageSender {
  REVIEWER
  EDITOR
  AUTHOR
}

enum Role {
  USER
  ADMIN
  REVIEWER
}

enum Pub_type {
  JOURNAL
  ARTICLE
  BOOK
  EBOOK
  AUDIOBOOK
}
