import { auth } from "@/auth";
import { prisma } from "../../../prisma";
import { Resend } from "resend";
import { NextResponse } from "next/server";
import { generateAndStorePdf } from "../../lib/pdf-service";
import Role from "../../../generated/prisma";

const resend = new Resend(process.env.RESEND_API_KEY);

export async function POST(request: Request) {
  const session = await auth();
  if (!session?.user?.id) return (
    console.log("Unauthorized"),
    NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  );

  try {
    const { title, abstract, content, keywords } = await request.json();
    const pdfUrl = await generateAndStorePdf(content);
    if (!pdfUrl) {
      console.log("Failed to generate PDF");
      return NextResponse.json(
        { error: "Failed to generate PDF" },
        { status: 500 }

      );
    }

    // Create manuscript record with error logging
    let manuscript;
    try {
      manuscript = await prisma.manuscript.create({
        data: {
          author_id: session.user.id,
          title,
          abstract,
          content,
          keywords,
          pdfFile: pdfUrl,
        },
      });
    } catch (prismaError: any) {
      console.error('Prisma manuscript.create failed:', prismaError, {
        author_id: session.user.id,
        title,
        abstract,
        content,
        keywords,
        pdfFile: pdfUrl,
      });
      return NextResponse.json(
        { error: 'Failed to create manuscript', details: prismaError && prismaError.message ? prismaError.message : prismaError },
        { status: 500 }
      );
    }

    // Get reviewers
    const reviewers = await prisma.user.findMany({
      where: { role: "REVIEWER" },
    });

    // Send notifications
    console.log(`📧 Starting email notifications for ${reviewers.length} reviewers`);
    console.log(`🔑 Resend API Key configured: ${process.env.RESEND_API_KEY ? 'Yes' : 'No'}`);

    if (!Array.isArray(reviewers) || reviewers.length === 0) {
      console.log("⚠️ No reviewers found. Skipping notifications.");
    } else {
      await Promise.all(
        reviewers.map(async (reviewer, index) => {
          try {
            console.log(`📝 Processing reviewer ${index + 1}/${reviewers.length}: ${reviewer.email}`);

            // Create notification
            await prisma.notification.create({
              data: {
                userId: reviewer.id,
                title: "New Manuscript Submitted",
                message: `New manuscript from ${session.user?.name} requires review`,
                type: "MANUSCRIPT_SUBMISSION",
                relatedId: manuscript.id,
              },
            });
            console.log(`✅ Notification created for ${reviewer.email}`);

            // Send email
            const emailResult = await resend.emails.send({
              from: 'ARRS System <<EMAIL>>',
              to: reviewer.email!,
              subject: "New Manuscript Assignment - Test",
              html: `
                <h2>New Manuscript Assignment</h2>
                <p>Hello ${reviewer.name || 'Reviewer'},</p>
                <p>A new manuscript titled "<strong>${manuscript.title}</strong>" has been submitted by ${session.user?.name} and requires your review.</p>
                <p>Please log in to the system to review the manuscript.</p>
                <p>Best regards,<br>ARRS System</p>
                <hr>
                <small>This is a test email from the development environment.</small>
              `,
            });
            console.log(`✅ Email sent successfully to ${reviewer.email}:`, emailResult);
          } catch (notifyErr) {
            console.error(`❌ Notification/email failed for reviewer ${reviewer.id} (${reviewer.email}):`, notifyErr);
            // Log more details about the error
            if (notifyErr instanceof Error) {
              console.error(`Error message: ${notifyErr.message}`);
              console.error(`Error stack: ${notifyErr.stack}`);
            }
          }
        })
      );
    }

    return NextResponse.json({ success: true, manuscript });
  } catch (error) {
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}